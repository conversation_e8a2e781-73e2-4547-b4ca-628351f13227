package com.shoalter.mms.store.api.adapter.controller;

import com.shoalter.mms.store.api.adapter.controller.dto.ResponseDto;
import com.shoalter.mms.store.api.adapter.controller.dto.UserDto;
import com.shoalter.mms.store.api.adapter.mms.store.dto.FindStoreBuRequest;
import com.shoalter.mms.store.api.adapter.mms.store.dto.FindStoresResponse;
import com.shoalter.mms.store.api.adapter.mms.store.usecase.FindStoresUseCase;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Set;

@RequiredArgsConstructor
@Tag(name = "Store Info")
@RequestMapping(path = "api/stores")
@RestController
public class StoreInfoController {
	private final FindStoresUseCase findStoresUseCase;


	/**
	 * Get store information, will return all stores according to the user's role and accessible stores
	 * This API is the v2 version of [GET]/api/stores/bu endpoint, enhanced with the user's role and accessible stores filter
	 */
	@Operation(description = "Get store information, will return all stores according to the user's role and accessible stores")
	@GetMapping("/storeInfo")
	public ResponseDto<List<FindStoresResponse>> getStoresInfo(
		@AuthenticationPrincipal UserDto user,
		@Schema(description = "'HKTV' or|and 'LITTLE_MALL', query both BU by default")
		@RequestParam(required = false) Set<String> buCodes,
		@Schema(description = "use user token merchant id if no input")
		@RequestParam(required = false) Integer merchantId,
		@Schema(description = "whether display inactive store or not (default false)")
		@RequestParam(defaultValue = "false") Boolean displayInactiveStore,
		@Schema(description = "filter by contract status(Active / Expired / Pending / Terminated)")
		@RequestParam(required = false) Set<String> contractStatus,
		@Schema(description = "whether including virtual store or not (default false)")
		@RequestParam(defaultValue = "false") Boolean isIncludingVirtualStore,
		@Schema(description = "GROUP_CHAT / ORDER / 'null' for all")
		@RequestParam(required = false) String source) {
		FindStoreBuRequest request = FindStoreBuRequest.builder()
			.buCodes(buCodes)
			.merchantId(merchantId)
			.displayInactiveStore(displayInactiveStore)
			.isIncludingVirtualStore(isIncludingVirtualStore)
			.contractStatus(contractStatus)
			.source(source)
			.requestType(FindStoreBuRequest.RequestType.NEW_FLOW)
			.build();
		return new ResponseDto<>(1, findStoresUseCase.getStores(user, request), null);
	}
}
