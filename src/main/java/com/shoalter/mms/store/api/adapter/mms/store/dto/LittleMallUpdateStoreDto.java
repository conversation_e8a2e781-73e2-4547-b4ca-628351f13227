package com.shoalter.mms.store.api.adapter.mms.store.dto;

import com.shoalter.mms.store.api.adapter.littlemall.dto.StoreDto;
import com.shoalter.mms.store.api.adapter.mms.user.dto.StoreStatusDto;
import lombok.Getter;
import lombok.Value;

@Value
@Getter
public class LittleMallUpdateStoreDto {
	public static final String LITTLE_MALL_STATUS_SUCCESS = "SUCCESS";

	boolean isSuccess;
	StoreDto storeDto;
	StoreStatusDto storeStatus;
	String status;

	/**
	 * Creates a successful response with all data
	 */
	public static LittleMallUpdateStoreDto success(StoreDto storeDto, StoreStatusDto storeStatus) {
		return new LittleMallUpdateStoreDto(true, storeDto, storeStatus, LITTLE_MALL_STATUS_SUCCESS);
	}

	/**
	 * Creates a failed response with error status
	 */
	public static LittleMallUpdateStoreDto failure(String errorStatus) {
		return new LittleMallUpdateStoreDto(false, null, null, errorStatus);
	}

	/**
	 * Creates a response based on the status string
	 */
	public static LittleMallUpdateStoreDto fromStatus(String status, StoreDto storeDto, StoreStatusDto storeStatus) {
		boolean isSuccess = LITTLE_MALL_STATUS_SUCCESS.equals(status);
		return new LittleMallUpdateStoreDto(isSuccess, storeDto, storeStatus, status);
	}
}
