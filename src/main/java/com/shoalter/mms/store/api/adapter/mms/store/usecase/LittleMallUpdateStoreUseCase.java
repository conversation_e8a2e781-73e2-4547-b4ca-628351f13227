package com.shoalter.mms.store.api.adapter.mms.store.usecase;

import com.shoalter.mms.store.api.adapter.controller.dto.UserDto;
import com.shoalter.mms.store.api.adapter.littlemall.LittleMallAdapter;
import com.shoalter.mms.store.api.adapter.littlemall.dto.ResponseBodyDto;
import com.shoalter.mms.store.api.adapter.littlemall.dto.StoreDto;
import com.shoalter.mms.store.api.adapter.littlemall.dto.UpdateStoreDto;
import com.shoalter.mms.store.api.adapter.mms.store.dao.entity.StoreEntity;
import com.shoalter.mms.store.api.adapter.mms.store.dao.repository.StoreRepository;
import com.shoalter.mms.store.api.adapter.mms.store.dto.LittleMallUpdateStoreDto;
import com.shoalter.mms.store.api.adapter.mms.user.dto.StoreStatusDto;
import com.shoalter.mms.store.api.adapter.mms.store.service.StoreStatusService;
import com.shoalter.mms.store.api.exception.WarningException;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class LittleMallUpdateStoreUseCase {
	private final LittleMallAdapter littleMallAdapter;

	private final StoreStatusService storeStatusService;

	private final UpdateStoreUseCase updateStoreUseCase;
	private final ChatSyncUseCase chatSyncUseCase;

	private final StoreRepository storeRepository;

	/**
	 * Execute business logic for updating little-mall store information
	 */
	public LittleMallUpdateStoreDto updateStore(UserDto user, String storeCode, UpdateStoreDto updateStore) {
		// Validate store existence: record exist in MMS database
		StoreEntity storeEntity = storeRepository.findByStorefrontStoreCode(storeCode)
			.orElseThrow(() -> new WarningException("message7", storeCode));

		// Get store information: call little-mall API
		ResponseBodyDto<StoreDto> storeInfoResponse = littleMallAdapter.getStoreInfo(storeCode, user.getMerchantId())
			.orElseThrow();

		if (!LittleMallUpdateStoreDto.LITTLE_MALL_STATUS_SUCCESS.equals(storeInfoResponse.getStatus())) {
			return LittleMallUpdateStoreDto.failure(storeInfoResponse.getStatus());
		}

		// Set store activity status
		StoreDto store = storeInfoResponse.getData();
		if (store != null) {
			updateStore.setIsActive(store.getIsActive());
		}

		// Check store status and set visibility: call MMS user API
		StoreStatusDto storeStatus = getStoreStatus(user, storeCode);
		if (!Boolean.TRUE.equals(storeStatus.getIsActive()) || !Boolean.TRUE.equals(storeStatus.getIsIdVerified())) {
			updateStore.setIsVisible(false);
		}

		// Update little-mall store information: call little-mall API
		ResponseBodyDto<StoreDto> updateStoreInfoResponse = littleMallAdapter.updateStoreInfo(storeCode, user.getMerchantId(), updateStore).orElseThrow();

		// If update successful, sync related information
		if (LittleMallUpdateStoreDto.LITTLE_MALL_STATUS_SUCCESS.equals(updateStoreInfoResponse.getStatus())) {
			updateStoreUseCase.updateStoreInfo(storeEntity, updateStore, user);
			chatSyncUseCase.syncThePlaceStoreInfoToChat(store, updateStoreInfoResponse.getData());
			return LittleMallUpdateStoreDto.success(updateStoreInfoResponse.getData(), storeStatus);
		}

		return LittleMallUpdateStoreDto.fromStatus(updateStoreInfoResponse.getStatus(), updateStoreInfoResponse.getData(), storeStatus);
	}

	private StoreStatusDto getStoreStatus(UserDto user, String storeCode) {
		if (user.getMerchantId() != null) {
			return storeStatusService.getStoreStatusByUserMerchant(user, storeCode);
		} else {
			return storeStatusService.getStoreStatusByStore(user, storeCode);
		}
	}
}
