package com.shoalter.mms.store.api.adapter.mms.store.usecase;

import com.shoalter.mms.store.api.adapter.chat.MdbAdapter;
import com.shoalter.mms.store.api.adapter.chat.dto.MdbVirtualStoreResponseDto;
import com.shoalter.mms.store.api.adapter.controller.dto.UserDto;
import com.shoalter.mms.store.api.adapter.littlemall.LittleMallAdapter;
import com.shoalter.mms.store.api.adapter.littlemall.dto.ResponseBodyDto;
import com.shoalter.mms.store.api.adapter.littlemall.dto.StoreDto;
import com.shoalter.mms.store.api.adapter.mms.product.dto.PageResult;
import com.shoalter.mms.store.api.adapter.mms.store.dao.entity.SystemParamEntity;
import com.shoalter.mms.store.api.adapter.mms.store.dao.projection.StoreContractDo;
import com.shoalter.mms.store.api.adapter.mms.store.dao.projection.StoreQueryDo;
import com.shoalter.mms.store.api.adapter.mms.store.dao.repository.StoreRepository;
import com.shoalter.mms.store.api.adapter.mms.store.dao.repository.SupplierVirtualStoreRepository;
import com.shoalter.mms.store.api.adapter.mms.store.dao.repository.SystemParamRepository;
import com.shoalter.mms.store.api.adapter.mms.store.dto.AccessibleStoreInfoResponseDto;
import com.shoalter.mms.store.api.adapter.mms.store.dto.FindStoreBuRequest;
import com.shoalter.mms.store.api.adapter.mms.store.dto.FindStoresResponse;
import com.shoalter.mms.store.api.adapter.mms.store.dto.FindThePlaceStorePageRequest;
import com.shoalter.mms.store.api.adapter.mms.store.dto.FindThePlaceStorePageResponse;
import com.shoalter.mms.store.api.adapter.mms.store.dto.QueryStoresDto;
import com.shoalter.mms.store.api.adapter.mms.whitelist.dao.entity.WhitelistEntity;
import com.shoalter.mms.store.api.adapter.mms.whitelist.dao.repository.WhitelistRepository;
import com.shoalter.mms.store.api.adapter.mms.whitelist.dao.repository.WhitelistRowRepository;
import com.shoalter.mms.store.api.constant.Constant;
import com.shoalter.mms.store.api.enums.ActiveIndEnum;
import com.shoalter.mms.store.api.enums.BuCodeEnum;
import com.shoalter.mms.store.api.enums.ContractStatusEnum;
import com.shoalter.mms.store.api.enums.ResponseStatusCode;
import com.shoalter.mms.store.api.enums.RoleCodeEnum;
import com.shoalter.mms.store.api.enums.RoleType;
import com.shoalter.mms.store.api.enums.WhitelistStatusEnum;
import com.shoalter.mms.store.api.enums.WhitelistTypeEnum;
import com.shoalter.mms.store.api.exception.WarningException;
import com.shoalter.mms.store.api.util.PageUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;


@RequiredArgsConstructor
@Service
public class FindStoresUseCase {

	private final StoreRepository storeRepository;
	private final WhitelistRepository whitelistRepository;
	private final WhitelistRowRepository whitelistRowRepository;
	private final LittleMallAdapter littleMallAdapter;
	private final SupplierVirtualStoreRepository supplierVirtualStoreRepository;
	private final SystemParamRepository systemParamRepository;
	private final MdbAdapter mdbAdapter;

	private final AccessibleStoreInfoService accessibleStoreInfoService;

	private final static String GROUP_CHAT_SOURCE = "GROUP_CHAT";
	private final static String ORDER_SOURCE = "ORDER";
	private static final String VIRTUAL_STORE_MERCHANT = "VIRTUAL_STORE_MERCHANT";


	public List<FindStoresResponse> getStores(UserDto userDto, FindStoreBuRequest request) {
		List<FindStoresResponse> storesResponses;
		List<StoreQueryDo> storesResult = getUserAccessStores(userDto, request);
		storesResponses = storesResult.stream().map(FindStoresResponse::generate).collect(Collectors.toList());
		if (Boolean.TRUE.equals(request.getIsIncludingVirtualStore())) {
			storesResponses.addAll(findVirtualStoresByRole(userDto, request.getSource()));
		}
		return storesResponses;
	}

	public PageResult<FindThePlaceStorePageResponse> getThePlaceStoresPages(UserDto userDto, FindThePlaceStorePageRequest request) {

		//get user access store from db
		FindStoreBuRequest data = FindStoreBuRequest.builder()
			.buCodes(Set.of(BuCodeEnum.LITTLE_MALL.name()))
			.merchantId(request.getMerchantId())
			.contractStatus(ContractStatusEnum.STORE_MGT_STATUS_ADMIN_SET)
			.displayInactiveStore(true)
			.build();
		List<StoreQueryDo> stores = getUserAccessStores(userDto, data);

		if (stores.isEmpty()) {
			return PageUtil.getPage(null, request.getPageNumber(), request.getPageSize());
		}

		//gather data
		List<String> storefrontStoreCodes = new ArrayList<>();
		List<Integer> storeIds = new ArrayList<>();
		Map<String, StoreQueryDo> storeInfoDbMap = new HashMap<>();
		stores.forEach(store -> {
			storefrontStoreCodes.add(store.getStorefrontStoreCode());
			storeIds.add(store.getStoreId());
			storeInfoDbMap.put(store.getStorefrontStoreCode(), store);
		});

		//get store data from little mall server
		Map<String, StoreDto> littleMallRequestResultMap = new HashMap<>();
		for (List<String> splitStoreCodes : ListUtils.partition(storefrontStoreCodes, 300)) {
			ResponseBodyDto<Map<String, StoreDto>> littleMallRequestResult = littleMallAdapter.getStoreInfoMap(splitStoreCodes, userDto.getMerchantId()).orElseThrow();
			if (!ResponseStatusCode.SUCCESS.name().equals(littleMallRequestResult.getStatus())) {
				throw new WarningException("message8", littleMallRequestResult.getStatus());
			}

			littleMallRequestResultMap.putAll(littleMallRequestResult.getData());
		}


		//get store contract status from db
		Map<String, StoreContractDo> storeContractMap = storeRepository.findStoreContractStatusByStoreIds(storeIds).stream()
			.collect(Collectors.toMap(StoreContractDo::getStorefrontStoreCode, Function.identity()));

		//combined data
		List<FindThePlaceStorePageResponse> results = new ArrayList<>();
		for (Map.Entry<String, StoreDto> entry : littleMallRequestResultMap.entrySet()) {
			String storefrontStoreCode = entry.getKey();
			StoreDto thePlaceStoreData = entry.getValue();
			StoreQueryDo dbStoreData = storeInfoDbMap.get(storefrontStoreCode);
			StoreContractDo storeContractDo = storeContractMap.get(storefrontStoreCode);
			HashSet<String> contractStatusSet = new HashSet<>(Arrays.asList(storeContractDo.getContractStatus().split(",")));
			//store contract is active and store is inactive = pending store, do not display
			if (contractStatusSet.contains(ContractStatusEnum.ACTIVE.getStatus()) && Constant.CONSTANT_NO.equals(dbStoreData.getActiveInd())) {
				continue;
			}
			results.add(FindThePlaceStorePageResponse.generate(thePlaceStoreData, dbStoreData, storeContractDo, contractStatusSet));
		}

		//filter by condition
		results = results.stream()
			.filter(store -> isMatchRequestFilter(request, store))
			.collect(Collectors.toList());

		return PageUtil.getPage(results, request.getPageNumber(), request.getPageSize());
	}

	private List<StoreQueryDo> getUserAccessStores(UserDto userDto, FindStoreBuRequest request) {
		QueryStoresDto queryStoresDto = QueryStoresDto.fetchQueryStoresDto(userDto, request.getBuCodes());
		if (request.getMerchantId() != null) {
			queryStoresDto.setMerchantId(request.getMerchantId());
		}

		if (ORDER_SOURCE.equals(request.getSource())) {
			request.setDisplayInactiveStore(true);
		}

		List<StoreQueryDo> stores = storeRepository.findByBuAndUser(queryStoresDto, request.getDisplayInactiveStore() ? null : ActiveIndEnum.Y.name());
		Set<String> storefrontStoreCodesFilterSet = getStoreFilterSet(userDto, request.getSource(), request.getContractStatus(), stores);
		if (FindStoreBuRequest.RequestType.NEW_FLOW == request.getRequestType()) {
			storefrontStoreCodesFilterSet = getRmFilterStoreSet(userDto, storefrontStoreCodesFilterSet);
		}
		return filterStores(stores, storefrontStoreCodesFilterSet);
	}

	Set<String> getRmFilterStoreSet(UserDto userDto, Set<String> storefrontStoreCodesFilterSet) {
		Integer userId = userDto.getUserId();
		String roleCode = userDto.getRoleCode();
		RoleType roleType = userDto.getRoleType();

		// only need to check accessible store info for role code in the list(RM, SRM, RML, RMO, DEPT_HEAD)
		if (AccessibleStoreInfoService.needCheckAccessibleStoreInfo(roleType, roleCode)) {
			// get accessible stores by role
			Set<String> filterSet = accessibleStoreInfoService.getAccessibleStores(userId, RoleCodeEnum.getEnum(roleCode))
				.stream()
				.map(AccessibleStoreInfoResponseDto::getStoreFrontStoreCode)
				.collect(Collectors.toSet());

			if (CollectionUtils.isNotEmpty(storefrontStoreCodesFilterSet)) {
				// Create a new mutable HashSet to avoid UnsupportedOperationException
				Set<String> mutableSet = new HashSet<>(storefrontStoreCodesFilterSet);
				mutableSet.retainAll(filterSet);
				return mutableSet;
			} else {
				return new HashSet<>(filterSet);
			}
		}

		// if role code is not in the list(RM, SRM, RML, RMO, DEPT_HEAD), return original set
		return storefrontStoreCodesFilterSet;
	}

	private Set<String> getStoreFilterSet(UserDto userDto, String source, Set<String> allBuContractStatusFilter, List<StoreQueryDo> stores) {
		//for group chat: storefront store code need to be in whitelist
		if (GROUP_CHAT_SOURCE.equals(source)) {
			return getWhitelistCodeByType(WhitelistTypeEnum.GROUP_CHAT_STORE);
		}

		//filter all bu stores by contract status
		Set<String> storefrontStoreCodesFilterSet = null;
		if (allBuContractStatusFilter != null) {
			List<Integer> allStoreIds = stores.stream()
				.filter(data -> data.getStorefrontStoreCode() != null && !data.getStorefrontStoreCode().isBlank())
				.map(StoreQueryDo::getStoreId)
				.collect(Collectors.toList());
			storefrontStoreCodesFilterSet = storeRepository.filterStoreByStoreIdAndContractStatus(allStoreIds, allBuContractStatusFilter);
		}

		//for order management: the place stores with contract status "Active" or "Expired"
		if (ORDER_SOURCE.equals(source)) {
			List<Integer> littleMallStoreIds = new ArrayList<>();
			Set<String> filterResult = new HashSet<>();
			for (StoreQueryDo store : stores) {
				if (BuCodeEnum.LITTLE_MALL.name().equals(store.getBusUnitCode())) {
					littleMallStoreIds.add(store.getStoreId());
				} else if (store.getStorefrontStoreCode() != null && Constant.CONSTANT_YES.equals(store.getActiveInd())) {
					filterResult.add(store.getStorefrontStoreCode());
				}
			}

			Set<String> status = RoleType.S == userDto.getRoleType() ? ContractStatusEnum.ORDER_STATUS_ADMIN_SET : ContractStatusEnum.ORDER_STATUS_MERCHANT_SET;
			filterResult.addAll(storeRepository.filterStoreByStoreIdAndContractStatus(littleMallStoreIds, status));
			if (allBuContractStatusFilter != null) {
				storefrontStoreCodesFilterSet = storefrontStoreCodesFilterSet.stream()
					.filter(filterResult::contains)
					.collect(Collectors.toSet());
			} else {
				storefrontStoreCodesFilterSet = filterResult;
			}
		}

		return storefrontStoreCodesFilterSet;
	}

	private Set<String> getWhitelistCodeByType(WhitelistTypeEnum whitelistType) {
		WhitelistEntity whitelistEntity = whitelistRepository.findByType(whitelistType.name()).orElseThrow();
		if (WhitelistStatusEnum.BLOCK.name().equals(whitelistEntity.getStatus())) {
			return Collections.emptySet();
		}

		if (WhitelistStatusEnum.ACTIVE.name().equals(whitelistEntity.getStatus())) {
			return whitelistRowRepository.findWhitelistCodeByWhitelistType(whitelistType.name());
		}

		return null;
	}

	private List<StoreQueryDo> filterStores(List<StoreQueryDo> stores, Set<String> storefrontStoreCodesFilterSet) {
		if (storefrontStoreCodesFilterSet == null) {
			return stores;
		}

		return stores.stream()
			.filter(store -> storefrontStoreCodesFilterSet.contains(store.getStorefrontStoreCode()))
			.collect(Collectors.toList());
	}

	private boolean isMatchRequestFilter(FindThePlaceStorePageRequest request, FindThePlaceStorePageResponse response) {

		if (request.getStorefrontStoreCode() != null &&
			(response.getCode() == null || !response.getCode().contains(request.getStorefrontStoreCode()))) {
			return false;
		}

		if (request.getStoreStatus() != null && !request.getStoreStatus().contains(response.getStoreStatus())) {
			return false;
		}

		if (request.getMerchantName() != null) {
			String merchantName = generateString(response.getMerchantNameEn()) + generateString(response.getStoreNameZh());
			if (!merchantName.toLowerCase().contains(request.getMerchantName().toLowerCase())) {
				return false;
			}
		}

		if (request.getStoreName() != null) {
			String storeName = generateString(response.getStoreNameEn()) + generateString(response.getStoreNameZh());
			if (!storeName.toLowerCase().contains(request.getStoreName().toLowerCase())) {
				return false;
			}
		}

		return true;
	}

	private String generateString(String request) {
		return request == null ? StringUtils.EMPTY : request;
	}

	private List<FindStoresResponse> findVirtualStoresByRole(UserDto userDto, String source) {

		List<MdbVirtualStoreResponseDto> virtualStores = new ArrayList<>();
		if (RoleCodeEnum.SUPPLIERS.getCode().equals(userDto.getRoleCode())) {
			virtualStores = mdbAdapter.getVirtualStoreLevel(supplierVirtualStoreRepository.findVirtualStoreByUserId(userDto.getUserId()));
		} else if (RoleType.S == userDto.getRoleType()) {
			virtualStores = mdbAdapter.getVirtualStoreLevel(supplierVirtualStoreRepository.findAllVirtualStoreCodes());
		}

		Set<String> groupChatWhitelistStores = null;
		if (GROUP_CHAT_SOURCE.equals(source)) {
			groupChatWhitelistStores = getWhitelistCodeByType(WhitelistTypeEnum.GROUP_CHAT_STORE);
		}

		return generateAndFilterVirtualStores(virtualStores, groupChatWhitelistStores);
	}

	private List<FindStoresResponse> generateAndFilterVirtualStores(List<MdbVirtualStoreResponseDto> virtualStores, Set<String> groupChatWhitelistStores) {
		if (CollectionUtils.isEmpty(virtualStores)) {
			return new ArrayList<>();
		}
		Map<String, List<SystemParamEntity>> virtualStoreSysParmCodeMap = systemParamRepository.findBySegment(VIRTUAL_STORE_MERCHANT).stream().collect(Collectors.groupingBy(SystemParamEntity::getCode));
		if (groupChatWhitelistStores == null || CollectionUtils.isEmpty(groupChatWhitelistStores)) {
			return virtualStores.stream().map(virtualStore -> FindStoresResponse.generateVirtualStore(virtualStore, virtualStoreSysParmCodeMap.get(virtualStore.getStoreCode()))).collect(Collectors.toList());
		}

		return virtualStores.stream()
			.filter(virtualStore -> groupChatWhitelistStores.contains(virtualStore.getStoreCode()))
			.map(virtualStore -> FindStoresResponse.generateVirtualStore(virtualStore, virtualStoreSysParmCodeMap.get(virtualStore.getStoreCode())))
			.collect(Collectors.toList());
	}
}
