package com.shoalter.mms.store.api.adapter.mms.store.usecase;

import com.shoalter.mms.store.api.adapter.controller.dto.SearchProductFieldOptionsRequest;
import com.shoalter.mms.store.api.adapter.littlemall.LittleMallAdapter;
import com.shoalter.mms.store.api.adapter.littlemall.dto.PidContent;
import com.shoalter.mms.store.api.adapter.littlemall.dto.ResponseBodyDto;
import com.shoalter.mms.store.api.adapter.littlemall.dto.StorePidContent;
import com.shoalter.mms.store.api.adapter.littlemall.dto.StoreProductFieldOptionDetail;
import com.shoalter.mms.store.api.adapter.littlemall.dto.VariantSearchRequestDto;
import com.shoalter.mms.store.api.adapter.littlemall.dto.VariantSearchResponseDto;
import com.shoalter.mms.store.api.adapter.littlemall.mapper.ProductFieldOptionDtoMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@RequiredArgsConstructor
@Service
public class ThePlaceProductFieldOptionUseCase {
	private final LittleMallAdapter littleMallAdapter;
	private final ProductFieldOptionDtoMapper productFieldOptionDtoMapper;

	public List<StorePidContent> getProductFieldOption(String storefrontStoreCode, Integer userMerchantId) {
		Optional<ResponseBodyDto<List<PidContent>>> storeVariants = littleMallAdapter.getStoreVariants(storefrontStoreCode, userMerchantId);

		if (storeVariants.isEmpty()) {
			log.error("No product field options found for storefrontStoreCode: {}, merchantId: {}", storefrontStoreCode, userMerchantId);
			return List.of();
		} else {
			return storeVariants.get().getData().stream()
				.map(productFieldOptionDtoMapper::mapToStorePidContent)
				.collect(Collectors.toList());
		}
	}

	public StoreProductFieldOptionDetail searchProductFieldOptionDetails(SearchProductFieldOptionsRequest request, Integer userMerchantId) {
		VariantSearchRequestDto searchRequestDto = VariantSearchRequestDto.builder().sids(request.getSids()).pids(request.getPids()).vids(request.getVids()).build();

		Optional<ResponseBodyDto<VariantSearchResponseDto>> searchStoreVariants = littleMallAdapter.searchStoreVariants(request.getStorefrontStoreCode(), userMerchantId, searchRequestDto);

		if (searchStoreVariants.isEmpty()) {
			log.error("No product field options details found for request: {}, merchantId: {}", request, userMerchantId);
			return new StoreProductFieldOptionDetail();
		} else {
			return productFieldOptionDtoMapper.mapToStoreProductFieldOptionDetail(searchStoreVariants.get().getData());
		}
	}
}
