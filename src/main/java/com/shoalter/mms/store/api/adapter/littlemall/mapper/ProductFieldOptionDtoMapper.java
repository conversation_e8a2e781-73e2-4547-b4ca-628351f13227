package com.shoalter.mms.store.api.adapter.littlemall.mapper;

import com.shoalter.mms.store.api.adapter.littlemall.dto.*;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Component
public class ProductFieldOptionDtoMapper {

    public StorePidContent mapToStorePidContent(PidContent pidContent) {
        if (pidContent == null) {
            return null;
        }

        StorePidContent storePidContent = new StorePidContent();
        storePidContent.setPid(pidContent.getPid());
        storePidContent.setNameEn(pidContent.getNameEn());
        storePidContent.setNameZh(pidContent.getNameZh());
        storePidContent.setType(pidContent.getType());
        storePidContent.setStoreCode(pidContent.getStoreCode());

        // Group VIDs by SID
		// if sid is null, then set it to -1
        Map<Integer, List<VidContent>> vidsBySid = pidContent.getVidContents().stream()
                .collect(Collectors.groupingBy(vid -> vid.getSid() != null ? vid.getSid() : -1));

        List<StoreSidVidContent> contents = new ArrayList<>();

        // Handle VIDs with SIDs
        if (pidContent.getSidContents() != null) {
            for (SidContent sidContent : pidContent.getSidContents()) {
                StoreSidVidContent storeSidVidContent = new StoreSidVidContent();

                // Map SID content
                StoreSidContent storeSidContent = new StoreSidContent();
                storeSidContent.setSid(sidContent.getSid());
                storeSidContent.setPid(sidContent.getPid());
                storeSidContent.setNameEn(sidContent.getNameEn());
                storeSidContent.setNameZh(sidContent.getNameZh());
                storeSidContent.setType(sidContent.getType());
                storeSidContent.setStoreCode(sidContent.getStoreCode());
                storeSidVidContent.setStoreSidContent(storeSidContent);

                // Map associated VIDs
                List<VidContent> sidsVids = vidsBySid.get(sidContent.getSid());
                if (sidsVids != null) {
                    storeSidVidContent.setStoreVidContents(mapToStoreVidContents(sidsVids));
                    contents.add(storeSidVidContent);
                }
            }
        }

        // Handle VIDs without SIDs (sid == null or sid == -1)
        List<VidContent> vidsWithoutSid = vidsBySid.get(-1);
        if (vidsWithoutSid != null && !vidsWithoutSid.isEmpty()) {
            StoreSidVidContent storeSidVidContent = new StoreSidVidContent();
            storeSidVidContent.setStoreSidContent(null);
            storeSidVidContent.setStoreVidContents(mapToStoreVidContents(vidsWithoutSid));
            contents.add(storeSidVidContent);
        }

        storePidContent.setContents(contents);
        return storePidContent;
    }

    private List<StoreVidContent> mapToStoreVidContents(List<VidContent> vidContents) {
        if (vidContents == null) {
            return Collections.emptyList();
        }

        return vidContents.stream()
                .map(vid -> {
                    StoreVidContent storeVidContent = new StoreVidContent();
					storeVidContent.setPid(vid.getPid());
					storeVidContent.setSid(vid.getSid());
                    storeVidContent.setVid(vid.getVid());
                    storeVidContent.setNameEn(vid.getNameEn());
                    storeVidContent.setNameZh(vid.getNameZh());
                    storeVidContent.setType(vid.getType());
                    return storeVidContent;
                })
                .collect(Collectors.toList());
    }

	public StoreProductFieldOptionDetail mapToStoreProductFieldOptionDetail(VariantSearchResponseDto data) {
        if (data == null) {
            return null;
        }

        StoreProductFieldOptionDetail result = new StoreProductFieldOptionDetail();

        // Map pids
        if (data.getPids() != null) {
            result.setPids(data.getPids().stream()
                    .map( pid -> {
						StorePidContent storePid = new StorePidContent();
						storePid.setPid(pid.getPid());
						storePid.setNameEn(pid.getNameEn());
						storePid.setNameZh(pid.getNameZh());
						storePid.setType(pid.getType());
						storePid.setStoreCode(pid.getStoreCode());
						return storePid;
					})
                    .collect(Collectors.toList()));
        }

        // Map sids
        if (data.getSids() != null) {
            result.setSids(data.getSids().stream()
                    .map(sid -> {
                        StoreSidContent storeSid = new StoreSidContent();
                        storeSid.setSid(sid.getSid());
                        storeSid.setPid(sid.getPid());
                        storeSid.setNameEn(sid.getNameEn());
                        storeSid.setNameZh(sid.getNameZh());
                        storeSid.setType(sid.getType());
                        storeSid.setStoreCode(sid.getStoreCode());
                        return storeSid;
                    })
                    .collect(Collectors.toList()));
        }

        // Map vids
        if (data.getVids() != null) {
            result.setVids(data.getVids().stream()
                    .map(vid -> {
                        StoreVidContent storeVid = new StoreVidContent();
						storeVid.setPid(vid.getPid());
						storeVid.setSid(vid.getSid());
                        storeVid.setVid(vid.getVid());
                        storeVid.setNameEn(vid.getNameEn());
                        storeVid.setNameZh(vid.getNameZh());
                        storeVid.setType(vid.getType());
                        return storeVid;
                    })
                    .collect(Collectors.toList()));
        }

        return result;
    }
}
