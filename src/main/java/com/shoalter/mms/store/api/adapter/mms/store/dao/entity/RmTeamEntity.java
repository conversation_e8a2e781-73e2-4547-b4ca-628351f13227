package com.shoalter.mms.store.api.adapter.mms.store.dao.entity;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.*;
import java.util.Date;

@Entity
@Getter
@Setter
@ToString
@RequiredArgsConstructor
@Table(name = "RM_TEAM")
public class RmTeamEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Integer id;

    @Column(name = "RM_CODE", nullable = false)
    private String rmCode;

    @Column(name = "USER_ID", nullable = false)
    private Integer userId;

    @Column(name = "DEPT_HEAD_ID")
    private Integer deptHeadId;

    @Column(name = "TEAM_LEADER_ID")
    private Integer teamLeaderId;

    @Column(name = "SRM_ID")
    private Integer srmId;

    @Column(name = "DEPT_CODE", nullable = false)
    private String deptCode;

    @Column(name = "DISQ_SEQ")
    private Integer disqSeq;

    @Column(name = "CREATED_BY")
    private String createdBy;

    @Column(name = "LAST_UPDATED_BY")
    private String lastUpdatedBy;

    @Column(name = "LAST_UPDATED_DATE")
    private Date lastUpdatedDate;

    @Column(name = "CREATED_DATE")
    private Date createdDate;
}
