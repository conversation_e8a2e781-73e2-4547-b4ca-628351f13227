package com.shoalter.mms.store.api.adapter.littlemall;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.shoalter.mms.store.api.adapter.http.HttpGateway;
import com.shoalter.mms.store.api.adapter.http.dto.HttpRequestDto;
import com.shoalter.mms.store.api.adapter.http.exception.HttpException;
import com.shoalter.mms.store.api.adapter.littlemall.dto.*;
import com.shoalter.mms.store.api.adapter.littlemall.helper.LittleMallTokenService;
import com.shoalter.mms.store.api.constant.Constant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Validated
@RequiredArgsConstructor
@Component
public class LittleMallAdapter {

	private final HttpGateway httpGateway;

	private final LittleMallTokenService littleMallTokenService;

	private final ObjectMapper objectMapper;

	@Value("${little-mall.base-url:http://localhost:8080}/internal/stores/%s/categories")
	private String categoriesUrlTemplate;
	@Value("${little-mall.base-url:http://localhost:8080}/internal/stores/%s/categories/%s")
	private String categoryUrlTemplate;
	@Value("${little-mall.base-url:http://localhost:8080}/internal/stores/%s/category-tree")
	private String categoryTreeUrlTemplate;

	@Value("${little-mall.base-url:http://localhost:8080}/internal/stores/%s/navigation")
	private String navigationUrlTemplate;
	@Value("${little-mall.base-url:http://localhost:8080}/internal/stores/%s/footer")
	private String footerUrlTemplate;

	@Value("${little-mall.base-url:http://localhost:8080}/internal/stores/%s/store-page-layout")
	private String pageLayoutUrlTemplate;
	@Value("${little-mall.base-url:http://localhost:8080}/internal/stores/%s/static-pages")
	private String staticPagesUrlTemplate;
	@Value("${little-mall.base-url:http://localhost:8080}/internal/stores/%s/static-pages/%s")
	private String staticPageByPageIdUrlTemplate;

	@Value("${little-mall.base-url:http://localhost:8080}/internal/stores/%s")
	private String storeBaseUrlTemplate;

	@Value("${little-mall.base-url:http://localhost:8080}/internal/stores?codes=%s")
	private String storeListBaseUrlTemplate;

	@Value("${little-mall.base-url:http://localhost:8080}/internal/stores/%s/delivery-providers")
	private String storeDeliverySettingTemplate;
	@Value("${little-mall.base-url:http://localhost:8080}/internal/stores/%s/delivery-providers/%s")
	private String storeDeliverySettingByProviderIdTemplate;

	@Value("${little-mall.base-url:http://localhost:8080}/internal/stores/%s/delivery-integrations")
	private String deliveryIntegrationsTemplate;

	@Value("${little-mall.base-url:http://localhost:8080}/internal/stores/%s/delivery-integrations/%s")
	private String deliveryIntegrationsByIdTemplate;

	@Value("${little-mall.base-url:http://localhost:8080}/internal/stores/%s/delivery-integrations/%s/setting")
	private String deliveryIntegrationsSettingByIdTemplate;

	@Value("${little-mall.base-url:http://localhost:8080}/internal/stores/%s/delivery-integrations/%s/pickup-address")
	private String deliveryIntegrationsPickupAddressByIdTemplate;

	@Value("${little-mall.base-url:http://localhost:8080}/internal/stores/%s/delivery-integrations/%s/status")
	private String deliveryIntegrationsStatusByIdTemplate;

	@Value("${little-mall.base-url:http://localhost:8080}/internal/stores/%s/delivery-integrations/%s/courier-services")
	private String deliveryIntegrationsCourierServicesByIdTemplate;

	@Value("${little-mall.base-url:http://localhost:8080}/internal/stores/%s/delivery-providers/shipany/estimated-shipping-rate")
	private String deliveryProvidersShipanyEstimatedShippingRateTemplate;

	@Value("${little-mall.base-url:http://localhost:8080}/internal/stores/%s/custom-domain")
	private String domainRoutingTemplate;

	@Value("${little-mall.base-url:http://localhost:8080}/internal/stores/%s/handle")
	private String storeHandleTemplate;

	@Value("${little-mall.base-url:http://localhost:8080}/internal/stores/%s/orders/terminate")
	private String terminatedStoreOrdersTemplate;

	// GET /internal/stores/{storeCode}/variants
	@Value("${little-mall.base-url:http://localhost:8080}/internal/stores/%s/variants")
	private String storeVariantsTemplate;

	//POST /internal/stores/{storeCode}/variants/search
	@Value("${little-mall.base-url:http://localhost:8080}/internal/stores/%s/variants:search")
	private String storeVariantsSearchTemplate;

	private <T, R> Optional<ResponseBodyDto<R>> exchangeForBody(HttpRequestDto<T, ResponseBodyDto<R>> request) {
		try {
			return httpGateway.exchangeForBody(request);
		} catch (HttpException e) {
			if (e.getCause() instanceof HttpStatusCodeException) {
				HttpStatusCodeException httpStatusCodeException = (HttpStatusCodeException) e.getCause();
				if (httpStatusCodeException.getStatusCode().is4xxClientError()) {
					try {
						ResponseBodyDto<R> response = objectMapper.readValue(httpStatusCodeException.getResponseBodyAsString(), new TypeReference<>() {
						});
						log.error(e.getMessage(), e);
						return Optional.ofNullable(response);
					} catch (JsonProcessingException ex) {
						throw e;
					}
				}
			}
			throw e;
		}
	}


	public Optional<ResponseBodyDto<List<DeliveryIntegrationDto>>> getDeliveryIntegrations(@NotEmpty String storeCode, Integer userMerchantId) {
		var request =
				HttpRequestDto.<Void, ResponseBodyDto<List<DeliveryIntegrationDto>>>builder()
				.url(String.format(deliveryIntegrationsTemplate, storeCode))
				.method(HttpMethod.GET)
				.headers(getHeaders(storeCode, userMerchantId))
				.resultTypeReference(new ParameterizedTypeReference<>() {
				})
				.build();
		return exchangeForBody(request);
	}

	public Optional<ResponseBodyDto<DeliveryIntegrationDto>> getDeliveryIntegrationsById(
			@NotEmpty String storeCode, Integer userMerchantId, String deliveryIntegrationId) {
		var request =
				HttpRequestDto.<Void, ResponseBodyDto<DeliveryIntegrationDto>>builder()
						.url(String.format(deliveryIntegrationsByIdTemplate, storeCode, deliveryIntegrationId))
						.method(HttpMethod.GET)
						.headers(getHeaders(storeCode, userMerchantId))
						.resultTypeReference(new ParameterizedTypeReference<>() {
						})
						.build();
		return exchangeForBody(request);
	}

	public Optional<ResponseBodyDto<DeliveryIntegrationDto>> createDeliveryIntegrations(
			@NotEmpty String storeCode, Integer userMerchantId, CreateDeliveryIntegrationBodyDto body) {
		var request =
				HttpRequestDto.<CreateDeliveryIntegrationBodyDto, ResponseBodyDto<DeliveryIntegrationDto>>builder()
						.url(String.format(deliveryIntegrationsTemplate, storeCode))
						.method(HttpMethod.POST)
						.headers(getHeaders(storeCode, userMerchantId))
						.body(body)
						.resultTypeReference(new ParameterizedTypeReference<>() {
						})
						.build();
		return exchangeForBody(request);
	}

	public Optional<ResponseBodyDto<DeliveryIntegrationDto>> updateDeliveryIntegrations(
			@NotEmpty String storeCode, Integer userMerchantId, String deliveryIntegrationId, DeliveryIntegrationSettingDto body) {
		var request =
				HttpRequestDto.<DeliveryIntegrationSettingDto, ResponseBodyDto<DeliveryIntegrationDto>>builder()
						.url(String.format(deliveryIntegrationsByIdTemplate, storeCode, deliveryIntegrationId))
						.method(HttpMethod.PUT)
						.headers(getHeaders(storeCode, userMerchantId))
						.body(body)
						.resultTypeReference(new ParameterizedTypeReference<>() {
						})
						.build();
		return exchangeForBody(request);
	}

	public Optional<ResponseBodyDto<DeliveryIntegrationDto>> updateDeliveryIntegrationsSetting(
			@NotEmpty String storeCode, Integer userMerchantId, String deliveryIntegrationId,
			UpdateDeliveryIntegrationSettingDto body) {
		var request =
				HttpRequestDto.<UpdateDeliveryIntegrationSettingDto, ResponseBodyDto<DeliveryIntegrationDto>>builder()
						.url(String.format(deliveryIntegrationsSettingByIdTemplate, storeCode, deliveryIntegrationId))
						.method(HttpMethod.PUT)
						.headers(getHeaders(storeCode, userMerchantId))
						.body(body)
						.resultTypeReference(new ParameterizedTypeReference<>() {
						})
						.build();
		return exchangeForBody(request);
	}

	public Optional<ResponseBodyDto<DeliveryIntegrationDto>> updateDeliveryIntegrationsPickupAddress(
			@NotEmpty String storeCode, Integer userMerchantId, String deliveryIntegrationId,
			UpdateDeliveryIntegrationPickupAddressBodyDto body) {
		var request =
				HttpRequestDto.<UpdateDeliveryIntegrationPickupAddressBodyDto, ResponseBodyDto<DeliveryIntegrationDto>>builder()
						.url(String.format(deliveryIntegrationsPickupAddressByIdTemplate, storeCode, deliveryIntegrationId))
						.method(HttpMethod.PUT)
						.headers(getHeaders(storeCode, userMerchantId))
						.body(body)
						.resultTypeReference(new ParameterizedTypeReference<>() {
						})
						.build();
		return exchangeForBody(request);
	}

	public Optional<ResponseBodyDto<DeliveryIntegrationStatusDto>> findDeliveryIntegrationsStatus(
			@NotEmpty String storeCode, Integer userMerchantId, String deliveryIntegrationId) {
		var request =
				HttpRequestDto.<Void, ResponseBodyDto<DeliveryIntegrationStatusDto>>builder()
						.url(String.format(deliveryIntegrationsStatusByIdTemplate, storeCode, deliveryIntegrationId))
						.method(HttpMethod.GET)
						.headers(getHeaders(storeCode, userMerchantId))
						.resultTypeReference(new ParameterizedTypeReference<>() {
						})
						.build();
		return exchangeForBody(request);
	}

	public Optional<ResponseBodyDto<DeliveryIntegrationCourierServicesDto>> findDeliveryIntegrationsCourierServices(
			@NotEmpty String storeCode, Integer userMerchantId, String deliveryIntegrationId) {
		var request =
				HttpRequestDto.<Void, ResponseBodyDto<DeliveryIntegrationCourierServicesDto>>builder()
						.url(String.format(deliveryIntegrationsCourierServicesByIdTemplate, storeCode, deliveryIntegrationId))
						.method(HttpMethod.GET)
						.headers(getHeaders(storeCode, userMerchantId))
						.resultTypeReference(new ParameterizedTypeReference<>() {
						})
						.build();
		return exchangeForBody(request);
	}

	public Optional<ResponseBodyDto<DeliveryProvidersShipanyEstimatedShippingRateDto>> createDeliveryProvidersShipanyEstimatedShippingRate(
			@NotEmpty String storeCode, Integer userMerchantId, DeliveryProvidersShipanyEstimatedShippingRateBodyDto body) {
		var request =
				HttpRequestDto.<DeliveryProvidersShipanyEstimatedShippingRateBodyDto, ResponseBodyDto<DeliveryProvidersShipanyEstimatedShippingRateDto>>builder()
						.url(String.format(deliveryProvidersShipanyEstimatedShippingRateTemplate, storeCode))
						.method(HttpMethod.POST)
						.headers(getHeaders(storeCode, userMerchantId))
						.body(body)
						.resultTypeReference(new ParameterizedTypeReference<>() {
						})
						.build();
		return exchangeForBody(request);
	}

	public Optional<ResponseBodyDto<List<CategoryDto>>> getCategories(@NotEmpty String storeCode, Integer userMerchantId) {
		var request =
				HttpRequestDto.<Void, ResponseBodyDto<List<CategoryDto>>>builder()
				.url(String.format(deliveryIntegrationsByIdTemplate, storeCode))
				.method(HttpMethod.GET)
				.headers(getHeaders(storeCode, userMerchantId))
				.resultTypeReference(new ParameterizedTypeReference<>() {
				})
				.build();
		return exchangeForBody(request);
	}

	public Optional<ResponseBodyDto<CategoryDto>> getCategoryByCode(@NotEmpty String storeCode, @NotEmpty String categoryCode, Integer userMerchantId) {
		var request = HttpRequestDto.<Void, ResponseBodyDto<CategoryDto>>builder()
				.url(String.format(categoryUrlTemplate, storeCode, categoryCode))
				.method(HttpMethod.GET)
				.headers(getHeaders(storeCode, userMerchantId))
				.resultTypeReference(new ParameterizedTypeReference<>() {
				})
				.build();
		return exchangeForBody(request);
	}

	public Optional<ResponseBodyDto<List<CategoryDto>>> saveCategories(@NotEmpty String storeCode, Integer userMerchantId, @NotNull List<SaveCategoryDto> categoryList) {
		var request = HttpRequestDto.<List<SaveCategoryDto>, ResponseBodyDto<List<CategoryDto>>>builder()
				.url(String.format(categoriesUrlTemplate, storeCode))
				.method(HttpMethod.PUT)
				.headers(getHeaders(storeCode, userMerchantId))
				.body(categoryList)
				.resultTypeReference(new ParameterizedTypeReference<>() {
				})
				.build();
		return exchangeForBody(request);
	}

	public Optional<ResponseBodyDto<CategoryDto>> deleteCategoryByCode(@NotEmpty String storeCode, @NotEmpty String categoryCode, Integer userMerchantId) {
		var request = HttpRequestDto.<Void, ResponseBodyDto<CategoryDto>>builder()
				.url(String.format(categoryUrlTemplate, storeCode, categoryCode))
				.method(HttpMethod.DELETE)
				.headers(getHeaders(storeCode, userMerchantId))
				.resultTypeReference(new ParameterizedTypeReference<>() {
				})
				.build();
		return exchangeForBody(request);
	}

	public Optional<ResponseBodyDto<List<CategoryTreeDto>>> getCategoryTree(@NotEmpty String storeCode, Integer userMerchantId) {
		var request = HttpRequestDto.<Void, ResponseBodyDto<List<CategoryTreeDto>>>builder()
				.url(String.format(categoryTreeUrlTemplate, storeCode))
				.method(HttpMethod.GET)
				.headers(getHeaders(storeCode, userMerchantId))
				.resultTypeReference(new ParameterizedTypeReference<>() {
				})
				.build();
		return exchangeForBody(request);
	}

	public Optional<ResponseBodyDto<List<CategoryTreeDto>>> updateCategoryTree(@NotEmpty String storeCode, Integer userMerchantId, @NotNull List<CategoryTreeDto> categoryTree) {
		var request = HttpRequestDto.<List<CategoryTreeDto>, ResponseBodyDto<List<CategoryTreeDto>>>builder()
				.url(String.format(categoryTreeUrlTemplate, storeCode))
				.method(HttpMethod.PUT)
				.headers(getHeaders(storeCode, userMerchantId))
				.body(categoryTree)
				.resultTypeReference(new ParameterizedTypeReference<>() {
				})
				.build();
		return exchangeForBody(request);
	}

	public Optional<ResponseBodyDto<List<NavigationItemDto>>> getNavigationStructure(@NotEmpty String storeCode, Integer userMerchantId) {
		var request = HttpRequestDto.<Void, ResponseBodyDto<List<NavigationItemDto>>>builder()
				.url(String.format(navigationUrlTemplate, storeCode))
				.method(HttpMethod.GET)
				.headers(getHeaders(storeCode, userMerchantId))
				.resultTypeReference(new ParameterizedTypeReference<>() {
				})
				.build();
		return exchangeForBody(request);
	}

	public Optional<ResponseBodyDto<List<NavigationItemDto>>> updateNavigationStructure(@NotEmpty String storeCode, Integer userMerchantId, @NotNull List<NavigationItemDto> items) {
		var request = HttpRequestDto.<List<NavigationItemDto>, ResponseBodyDto<List<NavigationItemDto>>>builder()
				.url(String.format(navigationUrlTemplate, storeCode))
				.method(HttpMethod.PUT)
				.headers(getHeaders(storeCode, userMerchantId))
				.body(items)
				.resultTypeReference(new ParameterizedTypeReference<>() {
				})
				.build();
		return exchangeForBody(request);
	}

	public Optional<ResponseBodyDto<FooterItemDto>> getFooterStructure(@NotEmpty String storeCode, Integer userMerchantId) {
		var request = HttpRequestDto.<Void, ResponseBodyDto<FooterItemDto>>builder()
				.url(String.format(footerUrlTemplate, storeCode))
				.method(HttpMethod.GET)
				.headers(getHeaders(storeCode, userMerchantId))
				.resultTypeReference(new ParameterizedTypeReference<>() {
				})
				.build();
		return exchangeForBody(request);
	}

	public Optional<ResponseBodyDto<FooterItemDto>> updateFooterStructure(@NotEmpty String storeCode, Integer userMerchantId, @NotNull FooterItemDto item) {
		var request = HttpRequestDto.<FooterItemDto, ResponseBodyDto<FooterItemDto>>builder()
				.url(String.format(footerUrlTemplate, storeCode))
				.method(HttpMethod.PUT)
				.headers(getHeaders(storeCode, userMerchantId))
				.body(item)
				.resultTypeReference(new ParameterizedTypeReference<>() {
				})
				.build();
		return exchangeForBody(request);
	}

	public Optional<ResponseBodyDto<List<StorePageLayoutDto>>> getStorePageLayout(@NotEmpty String storeCode, Integer userMerchantId) {
		var request = HttpRequestDto.<Void, ResponseBodyDto<List<StorePageLayoutDto>>>builder()
				.url(String.format(pageLayoutUrlTemplate, storeCode))
				.method(HttpMethod.GET)
				.headers(getHeaders(storeCode, userMerchantId))
				.resultTypeReference(new ParameterizedTypeReference<>() {
				})
				.build();
		return exchangeForBody(request);
	}

	public Optional<ResponseBodyDto<List<StorePageLayoutDto>>> updateStorePageLayout(@NotEmpty String storeCode, Integer userMerchantId, @NotNull List<StorePageLayoutDto> pageLayout) {
		var request = HttpRequestDto.<List<StorePageLayoutDto>, ResponseBodyDto<List<StorePageLayoutDto>>>builder()
				.url(String.format(pageLayoutUrlTemplate, storeCode))
				.method(HttpMethod.PUT)
				.headers(getHeaders(storeCode, userMerchantId))
				.body(pageLayout)
				.resultTypeReference(new ParameterizedTypeReference<>() {
				})
				.build();
		return exchangeForBody(request);
	}

	public Optional<ResponseBodyDto<List<StaticPageDto>>> getStaticPages(@NotEmpty String storeCode, Integer userMerchantId) {
		var request = HttpRequestDto.<Void, ResponseBodyDto<List<StaticPageDto>>>builder()
				.url(String.format(staticPagesUrlTemplate, storeCode))
				.method(HttpMethod.GET)
				.headers(getHeaders(storeCode, userMerchantId))
				.resultTypeReference(new ParameterizedTypeReference<>() {
				})
				.build();
		return exchangeForBody(request);
	}

	public Optional<ResponseBodyDto<StaticPageDto>> getStaticPageById(@NotEmpty String storeCode, @NotEmpty String pageId, Integer userMerchantId) {
		var request = HttpRequestDto.<Void, ResponseBodyDto<StaticPageDto>>builder()
				.url(String.format(staticPageByPageIdUrlTemplate, storeCode, pageId))
				.method(HttpMethod.GET)
				.headers(getHeaders(storeCode, userMerchantId))
				.resultTypeReference(new ParameterizedTypeReference<>() {
				})
				.build();
		return exchangeForBody(request);
	}

	public Optional<ResponseBodyDto<StaticPageDto>> createStaticPage(@NotEmpty String storeCode, Integer userMerchantId, @NotNull SaveStaticPageDto page) {
		var request = HttpRequestDto.<SaveStaticPageDto, ResponseBodyDto<StaticPageDto>>builder()
				.url(String.format(staticPagesUrlTemplate, storeCode))
				.method(HttpMethod.POST)
				.headers(getHeaders(storeCode, userMerchantId))
				.body(page)
				.resultTypeReference(new ParameterizedTypeReference<>() {
				})
				.build();
		return exchangeForBody(request);
	}

	public Optional<ResponseBodyDto<StaticPageDto>> updateStaticPage(@NotEmpty String storeCode, @NotEmpty String pageId, Integer userMerchantId, @NotNull SaveStaticPageDto page) {
		var request = HttpRequestDto.<SaveStaticPageDto, ResponseBodyDto<StaticPageDto>>builder()
				.url(String.format(staticPageByPageIdUrlTemplate, storeCode, pageId))
				.method(HttpMethod.PUT)
				.headers(getHeaders(storeCode, userMerchantId))
				.body(page)
				.resultTypeReference(new ParameterizedTypeReference<>() {
				})
				.build();
		return exchangeForBody(request);
	}

	public Optional<ResponseBodyDto<Void>> deleteStaticPage(@NotEmpty String storeCode, @NotEmpty String pageId, Integer userMerchantId) {
		var request = HttpRequestDto.<StaticPageDto, ResponseBodyDto<Void>>builder()
				.url(String.format(staticPageByPageIdUrlTemplate, storeCode, pageId))
				.method(HttpMethod.DELETE)
				.headers(getHeaders(storeCode, userMerchantId))
				.resultTypeReference(new ParameterizedTypeReference<>() {
				})
				.build();
		return exchangeForBody(request);
	}

	public Optional<ResponseBodyDto<StoreDto>> createStore(@NotEmpty String storeCode, Integer userMerchantId, @NotNull CreateStoreDto store) {
		var request = HttpRequestDto.<CreateStoreDto, ResponseBodyDto<StoreDto>>builder()
				.url(String.format(storeBaseUrlTemplate, storeCode))
				.method(HttpMethod.POST)
				.headers(getHeaders(null, userMerchantId))
				.body(store)
				.resultTypeReference(new ParameterizedTypeReference<>() {
				})
				.build();
		return exchangeForBody(request);

	}

	public Optional<ResponseBodyDto<StoreDto>> updateStoreInfo(@NotEmpty String storeCode, Integer userMerchantId, @NotNull UpdateStoreDto store) {
		var request = HttpRequestDto.<UpdateStoreDto, ResponseBodyDto<StoreDto>>builder()
				.url(String.format(storeBaseUrlTemplate, storeCode))
				.method(HttpMethod.PUT)
				.headers(getHeaders(storeCode, userMerchantId))
				.body(store)
				.resultTypeReference(new ParameterizedTypeReference<>() {
				})
				.build();
		return exchangeForBody(request);
	}

	public Optional<ResponseBodyDto<StoreDto>> updateStoreStatus(@NotEmpty String storeCode, @NotNull UpdateStoreStatusDto store) {
		var request = HttpRequestDto.<UpdateStoreStatusDto, ResponseBodyDto<StoreDto>>builder()
				.url(String.format(storeBaseUrlTemplate, storeCode))
				.method(HttpMethod.PATCH)
				.headers(getHeaders(storeCode, null))
				.body(store)
				.resultTypeReference(new ParameterizedTypeReference<>() {
				})
				.build();
		return exchangeForBody(request);
	}

	public Optional<ResponseBodyDto<Void>> terminateStoreOrders(@NotEmpty String storeCode) {
		var request = HttpRequestDto.<Void, ResponseBodyDto<Void>>builder()
				.url(String.format(terminatedStoreOrdersTemplate, storeCode))
				.method(HttpMethod.POST)
				.headers(getHeaders(storeCode, null))
				.resultTypeReference(new ParameterizedTypeReference<>() {
				})
				.build();
		return exchangeForBody(request);
	}

	public Optional<ResponseBodyDto<StoreDto>> getStoreInfo(@NotEmpty String storeCode, Integer userMerchantId) {
		var request = HttpRequestDto.<Void, ResponseBodyDto<StoreDto>>builder()
				.url(String.format(storeBaseUrlTemplate, storeCode))
				.method(HttpMethod.GET)
				.headers(getHeaders(storeCode, userMerchantId))
				.resultTypeReference(new ParameterizedTypeReference<>() {
				})
				.build();
		return exchangeForBody(request);
	}

	public Optional<ResponseBodyDto<Map<String, StoreDto>>> getStoreInfoMap(List<String> storeList, Integer userMerchantId) {
		StringBuilder queryStringBuilder = new StringBuilder();
		for(int i = 0; i < storeList.size(); i++) {
			if(i > 0) {
				queryStringBuilder.append(",");
			}
			queryStringBuilder.append(storeList.get(i));
		}
		var request = HttpRequestDto.<Void, ResponseBodyDto<Map<String, StoreDto>>>builder()
				.url(String.format(storeListBaseUrlTemplate, queryStringBuilder))
				.method(HttpMethod.GET)
				.headers(getHeaders(userMerchantId))
				.resultTypeReference(new ParameterizedTypeReference<>() {})
				.build();
		return exchangeForBody(request);
	}

	public Optional<ResponseBodyDto<PageDto<StoreDeliveryProviderItemDto>>> getStoreDeliveryProviderList(
			@NotEmpty String storeCode, Integer userMerchantId, @NotNull Integer page, @NotNull Integer size,
			String sort, String direction, String deliveryType) {


		UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(String.format(storeDeliverySettingTemplate, storeCode))
				.queryParam("page", page)
				.queryParam("size", size);

		if(StringUtils.hasText(sort)) {
			builder = builder.queryParam("sort", sort);
		}

		if(StringUtils.hasText(direction)) {
			builder = builder.queryParam("direction", direction);
		}

		if(StringUtils.hasText(deliveryType)) {
			builder = builder.queryParam("delivery-type", deliveryType);
		}

		UriComponents uriComponents = builder.build();
		var request = HttpRequestDto.<Void, ResponseBodyDto<PageDto<StoreDeliveryProviderItemDto>>>builder()
				.url(uriComponents.toUriString())
				.method(HttpMethod.GET)
				.headers(getHeaders(storeCode, userMerchantId))
				.resultTypeReference(new ParameterizedTypeReference<>() {
				})
				.build();
		return exchangeForBody(request);
	}

	public Optional<ResponseBodyDto<StoreDeliveryProviderDto>> getStoreDeliveryProvider(@NotEmpty String storeCode, Integer userMerchantId, @NotNull Integer providerId) {
		var request = HttpRequestDto.<Void, ResponseBodyDto<StoreDeliveryProviderDto>>builder()
				.url(String.format(storeDeliverySettingByProviderIdTemplate, storeCode, providerId))
				.method(HttpMethod.GET)
				.headers(getHeaders(storeCode, userMerchantId))
				.resultTypeReference(new ParameterizedTypeReference<>() {
				})
				.build();
		return exchangeForBody(request);
	}

	public Optional<ResponseBodyDto<StoreDeliveryProviderItemDto>> createStoreDeliveryProvider(@NotEmpty String storeCode, Integer userMerchantId, @NotNull SaveStoreDeliveryProviderDto saveStoreDeliveryProvider) {
		var request = HttpRequestDto.<SaveStoreDeliveryProviderDto, ResponseBodyDto<StoreDeliveryProviderItemDto>>builder()
				.url(String.format(storeDeliverySettingTemplate, storeCode))
				.method(HttpMethod.POST)
				.headers(getHeaders(storeCode, userMerchantId))
				.body(saveStoreDeliveryProvider)
				.resultTypeReference(new ParameterizedTypeReference<>() {
				})
				.build();
		return exchangeForBody(request);
	}

	public Optional<ResponseBodyDto<StoreDeliveryProviderDto>> updateStoreDeliveryProvider(@NotEmpty String storeCode, Integer userMerchantId, @NotNull Integer providerId, @NotNull SaveStoreDeliveryProviderDto saveStoreDeliveryProvider) {
		var request = HttpRequestDto.<SaveStoreDeliveryProviderDto, ResponseBodyDto<StoreDeliveryProviderDto>>builder()
				.url(String.format(storeDeliverySettingByProviderIdTemplate, storeCode, providerId))
				.method(HttpMethod.PUT)
				.headers(getHeaders(storeCode, userMerchantId))
				.body(saveStoreDeliveryProvider)
				.resultTypeReference(new ParameterizedTypeReference<>() {
				})
				.build();
		return exchangeForBody(request);
	}

	public Optional<ResponseBodyDto<StoreDeliveryProviderDto>> deleteStoreDeliveryProvider(@NotEmpty String storeCode, Integer userMerchantId, @NotNull Integer providerId) {
		var request = HttpRequestDto.<Void, ResponseBodyDto<StoreDeliveryProviderDto>>builder()
				.url(String.format(storeDeliverySettingByProviderIdTemplate, storeCode, providerId))
				.method(HttpMethod.DELETE)
				.headers(getHeaders(storeCode, userMerchantId))
				.resultTypeReference(new ParameterizedTypeReference<>() {
				})
				.build();
		return exchangeForBody(request);
	}

	public Optional<ResponseBodyDto<DomainListDto>> createStoreDomainRouting(
		@NotEmpty String storeCode, Integer userMerchantId, @NotNull String domain
	) {
		var request = HttpRequestDto.<DomainDto, ResponseBodyDto<DomainListDto>>builder()
			.url(String.format(domainRoutingTemplate, storeCode))
			.method(HttpMethod.POST)
			.headers(getHeaders(storeCode, userMerchantId))
			.body(DomainDto.builder().domain(domain).build())
			.resultTypeReference(new ParameterizedTypeReference<>() {
			})
			.build();
		return exchangeForBody(request);
	}

	public Optional<ResponseBodyDto<DomainListDto>> deleteStoreDomainRouting(
		@NotEmpty String storeCode, Integer userMerchantId, @NotNull String domain
	) {
		var request = HttpRequestDto.<DomainDto, ResponseBodyDto<DomainListDto>>builder()
			.url(String.format(domainRoutingTemplate, storeCode))
			.method(HttpMethod.DELETE)
			.headers(getHeaders(storeCode, userMerchantId))
			.body(DomainDto.builder().domain(domain).build())
			.resultTypeReference(new ParameterizedTypeReference<>() {
			})
			.build();
		return exchangeForBody(request);
	}

	public Optional<ResponseBodyDto<DomainListDto>> getStoreDomainRoutingList(
		@NotEmpty String storeCode, Integer userMerchantId
	) {
		var request = HttpRequestDto.<DomainDto, ResponseBodyDto<DomainListDto>>builder()
			.url(String.format(domainRoutingTemplate, storeCode))
			.method(HttpMethod.GET)
			.headers(getHeaders(storeCode, userMerchantId))
			.resultTypeReference(new ParameterizedTypeReference<>() {
			})
			.build();
		return exchangeForBody(request);
	}

	public Optional<ResponseBodyDto<StoreDto>> updateStoreHandle(@NotEmpty String storeCode, Integer userMerchantId, String handle) {
		var request = HttpRequestDto.<StoreHandleDto, ResponseBodyDto<StoreDto>>builder()
			.url(String.format(storeHandleTemplate, storeCode))
			.method(HttpMethod.PUT)
			.headers(getHeaders(storeCode, userMerchantId))
			.body(StoreHandleDto.builder().handle(handle).build())
			.resultTypeReference(new ParameterizedTypeReference<>() {
			})
			.build();
		return exchangeForBody(request);
	}

	public Optional<ResponseBodyDto<List<PidContent>>> getStoreVariants(@NotEmpty String storeCode, Integer userMerchantId) {
		var request = HttpRequestDto.<Void, ResponseBodyDto<List<PidContent>>>builder()
			.url(String.format(storeVariantsTemplate, storeCode))
			.method(HttpMethod.GET)
			.headers(getHeaders(storeCode, userMerchantId))
			.resultTypeReference(new ParameterizedTypeReference<>() {})
			.build();
		return exchangeForBody(request);
	}

	public Optional<ResponseBodyDto<VariantSearchResponseDto>> searchStoreVariants(
			@NotEmpty String storeCode,
			Integer userMerchantId,
			@NotNull VariantSearchRequestDto searchRequest) {
		var request = HttpRequestDto.<VariantSearchRequestDto, ResponseBodyDto<VariantSearchResponseDto>>builder()
				.url(String.format(storeVariantsSearchTemplate, storeCode))
				.method(HttpMethod.POST)
				.headers(getHeaders(storeCode, userMerchantId))
				.body(searchRequest)
				.resultTypeReference(new ParameterizedTypeReference<>() {})
				.build();
		return exchangeForBody(request);
	}

	private HttpHeaders getHeaders(String storeCode, Integer userMerchantId) {
		var headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.add(HttpHeaders.AUTHORIZATION, Constant.BEARER_SCHEME + littleMallTokenService.generateToken(storeCode, userMerchantId));
		return headers;
	}

	private HttpHeaders getHeaders(Integer userMerchantId) {
		var headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.add(HttpHeaders.AUTHORIZATION, Constant.BEARER_SCHEME + littleMallTokenService.generateToken(userMerchantId));
		return headers;
	}

}
