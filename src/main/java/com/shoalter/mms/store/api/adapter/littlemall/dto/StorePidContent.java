package com.shoalter.mms.store.api.adapter.littlemall.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StorePidContent {
	private Integer pid;
	private String nameEn;
	private String nameZh;
	private String type;
	private String storeCode;

	@JsonProperty("contents")
	private List<StoreSidVidContent> contents;
}
