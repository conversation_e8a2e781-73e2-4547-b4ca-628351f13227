package com.shoalter.mms.store.api.adapter.controller;


import com.shoalter.mms.store.api.adapter.controller.dto.ResponseDto;
import com.shoalter.mms.store.api.adapter.controller.dto.SearchProductFieldOptionsRequest;
import com.shoalter.mms.store.api.adapter.controller.dto.UserDto;
import com.shoalter.mms.store.api.adapter.littlemall.dto.StorePidContent;
import com.shoalter.mms.store.api.adapter.littlemall.dto.StoreProductFieldOptionDetail;
import com.shoalter.mms.store.api.adapter.mms.store.usecase.ThePlaceProductFieldOptionUseCase;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RequiredArgsConstructor
@Tag(name = "The-Place API")
@RequestMapping(path = "api/stores/the-place")
@RestController
public class ThePlaceController {

	private final ThePlaceProductFieldOptionUseCase thePlaceProductFieldOptionUseCase;

	@GetMapping("/{storefrontStoreCode}/product-field-options")
	public ResponseDto<List<StorePidContent>> getProductFieldOptions(
		@AuthenticationPrincipal UserDto user,
		@PathVariable String storefrontStoreCode) {
		log.info("Get product field options for storefrontStoreCode: {}, merchantId: {}", storefrontStoreCode, user.getMerchantId());

		List<StorePidContent> data = thePlaceProductFieldOptionUseCase.getProductFieldOption(storefrontStoreCode, user.getMerchantId());

		return ResponseDto.success(data);
	}


	@PostMapping("/search/product-field-options")
	public ResponseDto<StoreProductFieldOptionDetail> searchProductFieldOptions(
		@AuthenticationPrincipal UserDto user,
		@RequestBody SearchProductFieldOptionsRequest request) {
		log.info("Search product field options for request: {}, merchantId: {}", request, user.getMerchantId());

		StoreProductFieldOptionDetail data = thePlaceProductFieldOptionUseCase.searchProductFieldOptionDetails(request, user.getMerchantId());

		return ResponseDto.success(data);
	}
}
