package com.shoalter.mms.store.api.enums;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

@Getter
public enum RoleCodeEnum {
	MERCHANT_ADMIN("MERCHANT_ADMIN"),
	MERCHANT("MERCHANT"),
	DATABANK_USER("DATABANK_USER"),
	FOOD_SUPER_MERCHANT("FOOD_SUPER_MERCHANT"),
	PHY_STORE_MERCHANT("PHY_STORE_MERCHANT"),
	SUPER_SYSTEM_ADMIN("SUPER_SYSTEM_ADMIN"),
	SUPPLIERS("SUPPLIERS"),
	ADMIN("ADMIN"),
	RM("RM"),
	SRM("SRM"),
	RML("RML"),
	RMO("RMO"),
	RM_ADMIN("RM ADMIN"),
	OPERATION_ADMIN("OPERATION_ADMIN"),
	DEPT_HEAD("DEPT_HEAD");

	private final String code;
	RoleCodeEnum(String code) {
		this.code = code;
	}

	private static final Map<String, RoleCodeEnum> enumMap;

	static {
		enumMap = new HashMap<>();
		for (RoleCodeEnum position : RoleCodeEnum.values()) {
			enumMap.put(position.name(), position);
		}
	}

    public static RoleCodeEnum getEnum(String name) {
		return enumMap.get(name);
	}

	public static final Set<String> GROUP_CHAT_ADMIN_ROLE_LIST = Set.of(MERCHANT_ADMIN.getCode());

	public static final Set<String> GROUP_CHAT_PARTICIPANTS_ROLE_LIST = Set.of(MERCHANT.getCode(), DATABANK_USER.getCode(), FOOD_SUPER_MERCHANT.getCode(), PHY_STORE_MERCHANT.getCode());

	public static final Set<String> CUSTOMER_CHAT_ROLE_LIST = Set.of(MERCHANT_ADMIN.getCode(), RML.getCode(),
		SRM.getCode(), RM.getCode(), RM_ADMIN.getCode(), OPERATION_ADMIN.getCode());

	public static final Set<String> CUSTOM_DOMAIN_ADMIN_ROLE_LIST = Set.of(SUPER_SYSTEM_ADMIN.getCode(), ADMIN.getCode());
	public static final Set<String> CUSTOM_DOMAIN_MERCHANT_ROLE_LIST = Set.of(MERCHANT_ADMIN.getCode(), MERCHANT.getCode());
}
