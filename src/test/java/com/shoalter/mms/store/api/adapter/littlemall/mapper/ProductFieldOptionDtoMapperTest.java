package com.shoalter.mms.store.api.adapter.littlemall.mapper;

import com.shoalter.mms.store.api.adapter.littlemall.dto.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

class ProductFieldOptionDtoMapperTest {

    private ProductFieldOptionDtoMapper mapper;

    @BeforeEach
    void setUp() {
        mapper = new ProductFieldOptionDtoMapper();
    }

    @Test
    void mapToStoreProductFieldOptionDto_WithCompleteData_MapsAllFieldsCorrectly() {
        // Create test data
		PidContent input = createSampleProductFieldOptionDto();

        // Perform mapping
		StorePidContent pidContent = mapper.mapToStorePidContent(input);

        assertEquals(1, pidContent.getPid());
        assertEquals("colour", pidContent.getNameEn());
        assertEquals("顏色", pidContent.getNameZh());
        assertEquals("MALL", pidContent.getType());

        // Verify contents (SID-VID relationships)
        List<StoreSidVidContent> contents = pidContent.getContents();
        assertEquals(2, contents.size());

        // Verify first SID and its VIDs
        StoreSidVidContent firstSidContent = contents.get(0);
        assertNotNull(firstSidContent.getStoreSidContent());
        assertEquals(1, firstSidContent.getStoreSidContent().getSid());
        assertEquals("RED", firstSidContent.getStoreSidContent().getNameEn());
        assertEquals(2, firstSidContent.getStoreVidContents().size());

        // Verify second SID and its VIDs
        StoreSidVidContent secondSidContent = contents.get(1);
        assertNotNull(secondSidContent.getStoreSidContent());
        assertEquals(2, secondSidContent.getStoreSidContent().getSid());
        assertEquals("BLUE", secondSidContent.getStoreSidContent().getNameEn());
        assertEquals(1, secondSidContent.getStoreVidContents().size());
    }

    @Test
    void mapToStoreProductFieldOptionDto_WhenVidsExistWithoutSids_MapsCorrectly() {
        // Create a PID with VIDs but no SIDs
        PidContent pidContent = new PidContent();
        pidContent.setPid(1);
        pidContent.setNameEn("size");
        pidContent.setNameZh("尺寸");
        pidContent.setType("MALL");

        VidContent vid1 = new VidContent();
        vid1.setVid(1);
        vid1.setNameEn("S");
        vid1.setNameZh("小");
        vid1.setType("MALL");

        VidContent vid2 = new VidContent();
        vid2.setVid(2);
        vid2.setNameEn("M");
        vid2.setNameZh("中");
        vid2.setType("MALL");

        pidContent.setVidContents(Arrays.asList(vid1, vid2));

        ProductFieldOptionDto input = new ProductFieldOptionDto();
        input.setPidContents(Collections.singletonList(pidContent));

        // Perform mapping
		StorePidContent storePidContent = mapper.mapToStorePidContent(pidContent);

        // Verify the result
        assertEquals(1, storePidContent.getContents().size());

        StoreSidVidContent sidVidContent = storePidContent.getContents().get(0);
        assertNull(sidVidContent.getStoreSidContent());
        assertEquals(2, sidVidContent.getStoreVidContents().size());
    }

    @Test
    void mapToStoreProductFieldOptionDto_WithMultiplePidContents_MapsAllCorrectly() {
        // Create test data with multiple PIDs
        List<PidContent> pidContents = Arrays.asList(
                // First PID: Color with SIDs
                createColorPidContent(),
                // Second PID: Size without SIDs
                createSizePidContent(),
                // Third PID: Taste with mixed SIDs and standalone VIDs
                createTastePidContent()
        );

        // Perform mapping
        List<StorePidContent> result = pidContents.stream()
				.map(mapper::mapToStorePidContent)
				.collect(Collectors.toList());

        // Verify the result
        assertNotNull(result);
        assertEquals(3, result.size());

        // Verify Color PID (with SIDs)
        StorePidContent colorPid = result.get(0);
        assertEquals(1, colorPid.getPid());
        assertEquals("colour", colorPid.getNameEn());
        assertEquals("顏色", colorPid.getNameZh());
        assertEquals("MALL", colorPid.getType());
        assertEquals(2, colorPid.getContents().size());

        // Verify RED SID and its VIDs
        StoreSidVidContent redSidContent = colorPid.getContents().get(0);
        assertNotNull(redSidContent.getStoreSidContent());
        assertEquals(1, redSidContent.getStoreSidContent().getSid());
        assertEquals("RED", redSidContent.getStoreSidContent().getNameEn());
        assertEquals(2, redSidContent.getStoreVidContents().size());

        // Verify BLUE SID and its VIDs
        StoreSidVidContent blueSidContent = colorPid.getContents().get(1);
        assertNotNull(blueSidContent.getStoreSidContent());
        assertEquals(2, blueSidContent.getStoreSidContent().getSid());
        assertEquals("BLUE", blueSidContent.getStoreSidContent().getNameEn());
        assertEquals(1, blueSidContent.getStoreVidContents().size());

        // Verify Size PID (without SIDs)
        StorePidContent sizePid = result.get(1);
        assertEquals(2, sizePid.getPid());
        assertEquals("size", sizePid.getNameEn());
        assertEquals("尺寸", sizePid.getNameZh());
        assertEquals("MALL", sizePid.getType());
        assertEquals(1, sizePid.getContents().size());

        StoreSidVidContent sizeContent = sizePid.getContents().get(0);
        assertNull(sizeContent.getStoreSidContent());
        assertEquals(2, sizeContent.getStoreVidContents().size());
        assertEquals("S", sizeContent.getStoreVidContents().get(0).getNameEn());
        assertEquals("M", sizeContent.getStoreVidContents().get(1).getNameEn());

        // Verify Taste PID (mixed SIDs and standalone VIDs)
        StorePidContent tastePid = result.get(2);
        assertEquals(3, tastePid.getPid());
        assertEquals("taste", tastePid.getNameEn());
        assertEquals("口味", tastePid.getNameZh());
        assertEquals("CUSTOM", tastePid.getType());
        assertEquals(2, tastePid.getContents().size());

        // Verify Spicy SID and its VIDs
        StoreSidVidContent spicySidContent = tastePid.getContents().get(0);
        assertNotNull(spicySidContent.getStoreSidContent());
        assertEquals(3, spicySidContent.getStoreSidContent().getSid());
        assertEquals("SPICY", spicySidContent.getStoreSidContent().getNameEn());
        assertEquals(2, spicySidContent.getStoreVidContents().size());

        // Verify standalone VIDs
        StoreSidVidContent standaloneTasteContent = tastePid.getContents().get(1);
        assertNull(standaloneTasteContent.getStoreSidContent());
        assertEquals(1, standaloneTasteContent.getStoreVidContents().size());
        assertEquals("SOUR", standaloneTasteContent.getStoreVidContents().get(0).getNameEn());
    }

    @Test
    void mapToStoreProductFieldOptionDetail_WhenInputIsNull_ReturnsNull() {
        assertNull(mapper.mapToStoreProductFieldOptionDetail(null));
    }

    @Test
    void mapToStoreProductFieldOptionDetail_WhenInputIsEmpty_ReturnsEmptyLists() {
        VariantSearchResponseDto input = new VariantSearchResponseDto();

        StoreProductFieldOptionDetail result = mapper.mapToStoreProductFieldOptionDetail(input);

        assertNotNull(result);
        assertNull(result.getPids());
        assertNull(result.getSids());
        assertNull(result.getVids());
    }

    @Test
    void mapToStoreProductFieldOptionDetail_WithCompleteData_MapsAllFieldsCorrectly() {
        // Create test data
        VariantSearchResponseDto input = new VariantSearchResponseDto();

        // Set up PID
        PidContent pid = new PidContent();
        pid.setPid(1);
        pid.setNameEn("Color");
        pid.setNameZh("颜色");
        pid.setType("type1");
        pid.setStoreCode("store1");
        input.setPids(Collections.singletonList(pid));

        // Set up SID
        SidContent sid = new SidContent();
        sid.setSid(2);
        sid.setPid(1);
        sid.setNameEn("Red");
        sid.setNameZh("红色");
        sid.setType("type2");
        sid.setStoreCode("store2");
        input.setSids(Collections.singletonList(sid));

        // Set up VID
        VidContent vid = new VidContent();
        vid.setVid(3);
        vid.setNameEn("Dark Red");
        vid.setNameZh("深红色");
        vid.setType("type3");
        input.setVids(Collections.singletonList(vid));

        // Perform mapping
        StoreProductFieldOptionDetail result = mapper.mapToStoreProductFieldOptionDetail(input);

        // Verify the result
        assertNotNull(result);

        // Verify PIDs
        assertNotNull(result.getPids());
        assertEquals(1, result.getPids().size());
        StorePidContent mappedPid = result.getPids().get(0);
        assertEquals(1, mappedPid.getPid());
        assertEquals("Color", mappedPid.getNameEn());
        assertEquals("颜色", mappedPid.getNameZh());
        assertEquals("type1", mappedPid.getType());
        assertEquals("store1", mappedPid.getStoreCode());

        // Verify SIDs
        assertNotNull(result.getSids());
        assertEquals(1, result.getSids().size());
        StoreSidContent mappedSid = result.getSids().get(0);
        assertEquals(2, mappedSid.getSid());
        assertEquals(1, mappedSid.getPid());
        assertEquals("Red", mappedSid.getNameEn());
        assertEquals("红色", mappedSid.getNameZh());
        assertEquals("type2", mappedSid.getType());
        assertEquals("store2", mappedSid.getStoreCode());

        // Verify VIDs
        assertNotNull(result.getVids());
        assertEquals(1, result.getVids().size());
        StoreVidContent mappedVid = result.getVids().get(0);
        assertEquals(3, mappedVid.getVid());
        assertEquals("Dark Red", mappedVid.getNameEn());
        assertEquals("深红色", mappedVid.getNameZh());
        assertEquals("type3", mappedVid.getType());
    }

    private PidContent createColorPidContent() {
        // Reuse existing sample data for color
        return createSampleProductFieldOptionDto();
    }

    private PidContent createSizePidContent() {
        PidContent pidContent = new PidContent();
        pidContent.setPid(2);
        pidContent.setNameEn("size");
        pidContent.setNameZh("尺寸");
        pidContent.setType("MALL");

        VidContent vid1 = new VidContent();
        vid1.setVid(4);
        vid1.setPid(2);
        vid1.setNameEn("S");
        vid1.setNameZh("小");
        vid1.setType("MALL");

        VidContent vid2 = new VidContent();
        vid2.setVid(5);
        vid2.setPid(2);
        vid2.setNameEn("M");
        vid2.setNameZh("中");
        vid2.setType("MALL");

        pidContent.setVidContents(Arrays.asList(vid1, vid2));
        return pidContent;
    }

    private PidContent createTastePidContent() {
        PidContent pidContent = new PidContent();
        pidContent.setPid(3);
        pidContent.setNameEn("taste");
        pidContent.setNameZh("口味");
        pidContent.setType("CUSTOM");
        pidContent.setStoreCode("100024432S");

        // Create SID content for spicy
        SidContent spicySid = new SidContent();
        spicySid.setSid(3);
        spicySid.setPid(3);
        spicySid.setNameEn("SPICY");
        spicySid.setNameZh("辣");
        spicySid.setType("CUSTOM");
        spicySid.setStoreCode("100024432S");

        // Create VIDs for spicy SID
        VidContent spicyVid1 = new VidContent();
        spicyVid1.setVid(6);
        spicyVid1.setPid(3);
        spicyVid1.setSid(3);
        spicyVid1.setNameEn("MILD_SPICY");
        spicyVid1.setNameZh("小辣");
        spicyVid1.setType("CUSTOM");
        spicyVid1.setStoreCode("100024432S");

        VidContent spicyVid2 = new VidContent();
        spicyVid2.setVid(7);
        spicyVid2.setPid(3);
        spicyVid2.setSid(3);
        spicyVid2.setNameEn("VERY_SPICY");
        spicyVid2.setNameZh("大辣");
        spicyVid2.setType("CUSTOM");
        spicyVid2.setStoreCode("100024432S");

        // Create standalone VID (no SID)
        VidContent sourVid = new VidContent();
        sourVid.setVid(8);
        sourVid.setPid(3);
        sourVid.setNameEn("SOUR");
        sourVid.setNameZh("酸");
        sourVid.setType("CUSTOM");
        sourVid.setStoreCode("100024432S");

        pidContent.setSidContents(Collections.singletonList(spicySid));
        pidContent.setVidContents(Arrays.asList(spicyVid1, spicyVid2, sourVid));
        return pidContent;
    }

    private PidContent createSampleProductFieldOptionDto() {
        // Create VID contents
        VidContent vid1 = new VidContent();
        vid1.setVid(1);
        vid1.setPid(1);
        vid1.setSid(1);
        vid1.setNameEn("RED");
        vid1.setNameZh("紅色");
        vid1.setType("MALL");

        VidContent vid2 = new VidContent();
        vid2.setVid(2);
        vid2.setPid(1);
        vid2.setSid(1);
        vid2.setNameEn("DARK_RED");
        vid2.setNameZh("暗紅");
        vid2.setType("MALL");

        VidContent vid3 = new VidContent();
        vid3.setVid(3);
        vid3.setPid(1);
        vid3.setSid(2);
        vid3.setNameEn("BLUE");
        vid3.setNameZh("藍色");
        vid3.setType("MALL");

        // Create SID contents
        SidContent sid1 = new SidContent();
        sid1.setSid(1);
        sid1.setPid(1);
        sid1.setNameEn("RED");
        sid1.setNameZh("紅色");
        sid1.setType("MALL");

        SidContent sid2 = new SidContent();
        sid2.setSid(2);
        sid2.setPid(1);
        sid2.setNameEn("BLUE");
        sid2.setNameZh("藍色");
        sid2.setType("MALL");

        // Create PID content
        PidContent pidContent = new PidContent();
        pidContent.setPid(1);
        pidContent.setNameEn("colour");
        pidContent.setNameZh("顏色");
        pidContent.setType("MALL");
        pidContent.setSidContents(Arrays.asList(sid1, sid2));
        pidContent.setVidContents(Arrays.asList(vid1, vid2, vid3));

        return pidContent;
    }
}
